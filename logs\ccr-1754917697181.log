{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: Anthropic (endpoint: /v1/messages)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: gemini (endpoint: /v1beta/models/:modelAndAction)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: vertex-gemini (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: vertex-claude (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: deepseek (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: tooluse (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: openrouter (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: maxtoken (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: groq (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: cleancache (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: enhancetool (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: reasoning (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: sampling (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: maxcompletiontokens (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: cerebras (no endpoint)"}
{"level":30,"time":1754917697190,"pid":24600,"hostname":"LIU","msg":"register transformer: streamoptions (no endpoint)"}
{"level":30,"time":1754917697194,"pid":24600,"hostname":"LIU","msg":"ovoai provider registered"}
{"level":30,"time":1754917697236,"pid":24600,"hostname":"LIU","msg":"Server listening at http://127.0.0.1:3456"}
{"level":30,"time":1754917697236,"pid":24600,"hostname":"LIU","msg":"🚀 LLMs API server listening on http://127.0.0.1:3456"}
{"level":30,"time":1754917722434,"pid":24600,"hostname":"LIU","reqId":"req-1","req":{"method":"POST","url":"/v1/chat/completions","host":"127.0.0.1:3456","remoteAddress":"127.0.0.1","remotePort":64465},"msg":"incoming request"}
{"level":30,"time":1754917722437,"pid":24600,"hostname":"LIU","reqId":"req-1","body":{"messages":[{"content":"Hello! Please respond with a simple greeting.","role":"user"}],"model":"claude-3-5-sonnet-20241022","max_tokens":50},"msg":"request body"}
{"level":30,"time":1754917722437,"pid":24600,"hostname":"LIU","reqId":"req-1","msg":"Route POST:/v1/chat/completions not found"}
{"level":30,"time":1754917722439,"pid":24600,"hostname":"LIU","reqId":"req-1","res":{"statusCode":404},"responseTime":3.867799999192357,"msg":"request completed"}
