{"level":30,"time":1754917844017,"pid":35396,"hostname":"LI<PERSON>","msg":"register transformer: Anthropic (endpoint: /v1/messages)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: gemini (endpoint: /v1beta/models/:modelAndAction)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: vertex-gemini (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: vertex-claude (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: deepseek (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: tooluse (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: openrouter (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: maxtoken (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: groq (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: cleancache (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: enhancetool (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: reasoning (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: sampling (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: maxcompletiontokens (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: cerebras (no endpoint)"}
{"level":30,"time":1754917844017,"pid":35396,"hostname":"LIU","msg":"register transformer: streamoptions (no endpoint)"}
{"level":30,"time":1754917844020,"pid":35396,"hostname":"LIU","msg":"ovoai provider registered"}
{"level":30,"time":1754917844057,"pid":35396,"hostname":"LIU","msg":"Server listening at http://127.0.0.1:3456"}
{"level":30,"time":1754917844057,"pid":35396,"hostname":"LIU","msg":"🚀 LLMs API server listening on http://127.0.0.1:3456"}
{"level":30,"time":1754917861170,"pid":35396,"hostname":"LIU","reqId":"req-1","req":{"method":"POST","url":"/v1/chat/completions","host":"127.0.0.1:3456","remoteAddress":"127.0.0.1","remotePort":65264},"msg":"incoming request"}
{"level":30,"time":1754917861172,"pid":35396,"hostname":"LIU","reqId":"req-1","body":{"messages":[{"content":"Hello! Please respond with a simple greeting.","role":"user"}],"model":"claude-3-5-sonnet-20241022","max_tokens":50},"msg":"request body"}
{"level":30,"time":1754917861172,"pid":35396,"hostname":"LIU","reqId":"req-1","msg":"Route POST:/v1/chat/completions not found"}
{"level":30,"time":1754917861174,"pid":35396,"hostname":"LIU","reqId":"req-1","res":{"statusCode":404},"responseTime":3.6512000001966953,"msg":"request completed"}
{"level":30,"time":1754917891478,"pid":35396,"hostname":"LIU","reqId":"req-2","req":{"method":"POST","url":"/v1/messages","host":"127.0.0.1:3456","remoteAddress":"127.0.0.1","remotePort":65264},"msg":"incoming request"}
{"level":30,"time":1754917891482,"pid":35396,"hostname":"LIU","reqId":"req-2","body":{"messages":[{"content":"Hello! Please respond with a simple greeting.","role":"user"}],"model":"ovoai,gemini-2.5-pro","max_tokens":50},"msg":"request body"}
{"level":20,"time":1754917891483,"pid":35396,"hostname":"LIU","request":{"method":"POST","headers":{},"body":"{\"messages\":[{\"role\":\"user\",\"content\":\"Hello! Please respond with a simple greeting.\"}],\"model\":\"gemini-2.5-pro\",\"max_tokens\":50}","signal":{}},"requestUrl":"https://ovoai.apicenter.top/chat/completions","msg":"final request"}
{"level":50,"time":1754917894147,"pid":35396,"hostname":"LIU","reqId":"req-2","err":{"type":"SyntaxError","message":"Unexpected token '<', \"<!doctype \"... is not valid JSON","stack":"SyntaxError: Unexpected token '<', \"<!doctype \"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at parseJSONFromBytes (node:internal/deps/undici/undici:5738:19)\n    at successSteps (node:internal/deps/undici/undici:5719:27)\n    at fullyReadBody (node:internal/deps/undici/undici:4609:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async consumeBody (node:internal/deps/undici/undici:5728:7)\n    at async _s.transformResponseIn (C:\\Users\\<USER>\\.npm-global\\node_modules\\@musistudio\\claude-code-router\\dist\\cli.js:74988:15)\n    at async u0 (C:\\Users\\<USER>\\.npm-global\\node_modules\\@musistudio\\claude-code-router\\dist\\cli.js:74719:46)\n    at async o0 (C:\\Users\\<USER>\\.npm-global\\node_modules\\@musistudio\\claude-code-router\\dist\\cli.js:74678:116)"},"msg":"Unexpected token '<', \"<!doctype \"... is not valid JSON"}
{"level":30,"time":1754917894148,"pid":35396,"hostname":"LIU","reqId":"req-2","res":{"statusCode":500},"responseTime":2669.863600000739,"msg":"request completed"}
