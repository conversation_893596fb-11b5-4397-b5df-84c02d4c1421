# Network and API Diagnostic Script
Write-Host "=== Claude Code Router Network Diagnostic ===" -ForegroundColor Cyan
Write-Host ""

# Test 1: Basic network connectivity
Write-Host "1. Testing basic network connectivity..." -ForegroundColor Yellow
try {
    $ping = Test-Connection -ComputerName "*******" -Count 2 -Quiet
    if ($ping) {
        Write-Host "✅ Internet connection: OK" -ForegroundColor Green
    } else {
        Write-Host "❌ Internet connection: FAILED" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Internet connection test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: DNS resolution
Write-Host ""
Write-Host "2. Testing DNS resolution..." -ForegroundColor Yellow
try {
    $dns = Resolve-DnsName -Name "api.qdgf.top" -ErrorAction Stop
    Write-Host "✅ DNS resolution: OK" -ForegroundColor Green
    Write-Host "   IP Address: $($dns.IPAddress -join ', ')" -ForegroundColor Gray
} catch {
    Write-Host "❌ DNS resolution failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Port connectivity
Write-Host ""
Write-Host "3. Testing port connectivity..." -ForegroundColor Yellow
try {
    $connection = Test-NetConnection -ComputerName "api.qdgf.top" -Port 443 -InformationLevel Quiet
    if ($connection) {
        Write-Host "✅ Port 443 connectivity: OK" -ForegroundColor Green
    } else {
        Write-Host "❌ Port 443 connectivity: FAILED" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Port connectivity test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: HTTP/HTTPS connectivity
Write-Host ""
Write-Host "4. Testing HTTP/HTTPS connectivity..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://api.qdgf.top" -Method Head -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✅ HTTPS connectivity: OK" -ForegroundColor Green
    Write-Host "   Status Code: $($response.StatusCode)" -ForegroundColor Gray
} catch {
    Write-Host "❌ HTTPS connectivity failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: API endpoint test
Write-Host ""
Write-Host "5. Testing API endpoint..." -ForegroundColor Yellow
$headers = @{
    "Authorization" = "Bearer sk-r1ifeboa8V8Jb3irUxRcLNyW7D-Qa0SHlUuinCT_LbM0xUe7"
}

try {
    $response = Invoke-RestMethod -Uri "https://api.qdgf.top/v1/models" -Headers $headers -TimeoutSec 15 -ErrorAction Stop
    Write-Host "✅ API endpoint: OK" -ForegroundColor Green
    Write-Host "   Available models:" -ForegroundColor Gray
    if ($response.data) {
        foreach ($model in $response.data) {
            Write-Host "   - $($model.id)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ API endpoint test failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "   Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
    }
}

# Test 6: Simple chat completion test
Write-Host ""
Write-Host "6. Testing chat completion..." -ForegroundColor Yellow
$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer sk-r1ifeboa8V8Jb3irUxRcLNyW7D-Qa0SHlUuinCT_LbM0xUe7"
}

$body = @{
    model = "gemini-2.5-pro-search"
    messages = @(
        @{
            role = "user"
            content = "Hello, please respond with just 'API test successful'"
        }
    )
    max_tokens = 20
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri "https://api.qdgf.top/v1/chat/completions" -Method Post -Headers $headers -Body $body -TimeoutSec 30 -ErrorAction Stop
    Write-Host "✅ Chat completion: OK" -ForegroundColor Green
    if ($response.choices -and $response.choices.Count -gt 0) {
        Write-Host "   Response: $($response.choices[0].message.content)" -ForegroundColor Gray
    }
    Write-Host "   Model: $($response.model)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Chat completion failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "   Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "=== Diagnostic Complete ===" -ForegroundColor Cyan
