# Test different model names with the API
Write-Host "=== Testing QDGF API Models ===" -ForegroundColor Cyan
Write-Host ""

$apiKey = "sk-r1ifeboa8V8Jb3irUxRcLNyW7D-Qa0SHlUuinCT_LbM0xUe7"
$baseUrl = "https://api.qdgf.top"

$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $apiKey"
}

# Test different model names
$models = @(
    "gemini-2.5-pro",
    "gemini-2.5-flash", 
    "gemini-2.5-pro-search",
    "gemini-2.5-flash-search"
)

Write-Host "Testing API endpoint: $baseUrl/v1/chat/completions" -ForegroundColor Yellow
Write-Host ""

foreach ($model in $models) {
    Write-Host "Testing model: $model" -ForegroundColor Cyan
    
    $testBody = @{
        model = $model
        messages = @(
            @{
                role = "user"
                content = "Hello! Please respond with just 'Test successful for $model'"
            }
        )
        max_tokens = 30
        temperature = 0.7
    } | ConvertTo-Json -Depth 3

    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/v1/chat/completions" -Method Post -Headers $headers -Body $testBody -TimeoutSec 20
        Write-Host "✅ Success!" -ForegroundColor Green
        Write-Host "   Model returned: $($response.model)" -ForegroundColor Gray
        if ($response.choices -and $response.choices.Count -gt 0) {
            Write-Host "   Response: $($response.choices[0].message.content)" -ForegroundColor White
        }
        if ($response.usage) {
            Write-Host "   Usage: Input=$($response.usage.prompt_tokens), Output=$($response.usage.completion_tokens)" -ForegroundColor Gray
        }
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "❌ Failed [$statusCode]: $($_.Exception.Message)" -ForegroundColor Red
        
        if ($statusCode -eq 400) {
            Write-Host "   Possible invalid model name" -ForegroundColor Yellow
        } elseif ($statusCode -eq 401) {
            Write-Host "   Possible invalid API key" -ForegroundColor Yellow
        } elseif ($statusCode -eq 404) {
            Write-Host "   Endpoint not found" -ForegroundColor Yellow
        }
    }
    
    Write-Host ""
}

Write-Host "=== Test Complete ===" -ForegroundColor Cyan
