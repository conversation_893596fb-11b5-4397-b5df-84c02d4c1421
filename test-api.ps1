# Test OvoAI API Connection
$apiKey = "sk-KdujPK1u6uDT51tbZyLRkFn7rxhuFllaCJ37ioBVT8Fi8Uk8"
$baseUrl = "https://ovoai.apicenter.top"

# Test different endpoints
$endpoints = @(
    "$baseUrl/v1/chat/completions",
    "$baseUrl/api/v1/chat/completions",
    "$baseUrl/chat/completions"
)

$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $apiKey"
}

$testBody = @{
    model = "gpt-3.5-turbo"
    messages = @(
        @{
            role = "user"
            content = "Hello"
        }
    )
    max_tokens = 10
} | ConvertTo-Json -Depth 3

Write-Host "Starting OvoAI API test..."
Write-Host "API Key: $($apiKey.Substring(0,10))..."

foreach ($endpoint in $endpoints) {
    Write-Host ""
    Write-Host "Testing endpoint: $endpoint"
    try {
        $response = Invoke-RestMethod -Uri $endpoint -Method Post -Headers $headers -Body $testBody -TimeoutSec 10
        Write-Host "Success!" -ForegroundColor Green
        Write-Host "Model: $($response.model)"
        if ($response.choices -and $response.choices.Count -gt 0) {
            Write-Host "Response: $($response.choices[0].message.content)"
        }
        Write-Host "Usage: $($response.usage | ConvertTo-Json -Compress)"
        break
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        $statusDesc = $_.Exception.Response.StatusDescription
        Write-Host "Failed [$statusCode]: $statusDesc" -ForegroundColor Red

        if ($statusCode -eq 401) {
            Write-Host "   Possible invalid API key" -ForegroundColor Yellow
        } elseif ($statusCode -eq 404) {
            Write-Host "   Endpoint not found, trying next..." -ForegroundColor Yellow
        } elseif ($statusCode -eq 400) {
            Write-Host "   Bad request format" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "Test completed."
