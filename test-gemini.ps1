# Test Gemini models on OvoAI
$apiKey = "sk-KdujPK1u6uDT51tbZyLRkFn7rxhuFllaCJ37ioBVT8Fi8Uk8"
$endpoint = "https://ovoai.apicenter.top/chat/completions"

$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $apiKey"
}

# Test different models
$models = @("gemini-2.5-flash", "gemini-2.5-pro", "gpt-3.5-turbo", "gpt-4")

foreach ($model in $models) {
    Write-Host ""
    Write-Host "Testing model: $model" -ForegroundColor Cyan
    
    $testBody = @{
        model = $model
        messages = @(
            @{
                role = "user"
                content = "Hello! Please respond with just 'Hello from $model'"
            }
        )
        max_tokens = 50
        temperature = 0.7
    } | ConvertTo-Json -Depth 3

    try {
        $response = Invoke-RestMethod -Uri $endpoint -Method Post -Headers $headers -Body $testBody -TimeoutSec 15
        Write-Host "Success!" -ForegroundColor Green
        
        if ($response.choices -and $response.choices.Count -gt 0) {
            Write-Host "Response: $($response.choices[0].message.content)" -ForegroundColor White
        }
        
        if ($response.usage) {
            Write-Host "Usage - Input: $($response.usage.prompt_tokens), Output: $($response.usage.completion_tokens)" -ForegroundColor Gray
        }
        
        Write-Host "Model returned: $($response.model)" -ForegroundColor Gray
        
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        $statusDesc = $_.Exception.Response.StatusDescription
        Write-Host "Failed [$statusCode]: $statusDesc" -ForegroundColor Red
        
        if ($statusCode -eq 400) {
            Write-Host "   Model might not be supported" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "Test completed!" -ForegroundColor Green
