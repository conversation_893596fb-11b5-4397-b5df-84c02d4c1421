{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: Anthrop<PERSON> (endpoint: /v1/messages)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: gemini (endpoint: /v1beta/models/:modelAndAction)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: vertex-gemini (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: vertex-claude (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: deepseek (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: tooluse (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: openrouter (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: maxtoken (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: groq (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: cleancache (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: enhancetool (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: reasoning (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: sampling (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: maxcompletiontokens (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: cerebras (no endpoint)"}
{"level":30,"time":1754918111724,"pid":34592,"hostname":"LIU","msg":"register transformer: streamoptions (no endpoint)"}
{"level":30,"time":1754918111727,"pid":34592,"hostname":"LIU","msg":"ovoai provider registered"}
{"level":30,"time":1754918111767,"pid":34592,"hostname":"LIU","msg":"Server listening at http://127.0.0.1:3456"}
{"level":30,"time":1754918111767,"pid":34592,"hostname":"LIU","msg":"🚀 LLMs API server listening on http://127.0.0.1:3456"}
{"level":30,"time":1754918121677,"pid":34592,"hostname":"LIU","reqId":"req-1","req":{"method":"POST","url":"/v1/messages","host":"127.0.0.1:3456","remoteAddress":"127.0.0.1","remotePort":50567},"msg":"incoming request"}
{"level":30,"time":1754918121683,"pid":34592,"hostname":"LIU","reqId":"req-1","body":{"messages":[{"content":"Hello! Please respond with a simple greeting and tell me you are working through Claude Code Router.","role":"user"}],"model":"ovoai,gemini-2.5-pro","max_tokens":100},"msg":"request body"}
{"level":20,"time":1754918121684,"pid":34592,"hostname":"LIU","request":{"method":"POST","headers":{},"body":"{\"messages\":[{\"role\":\"user\",\"content\":\"Hello! Please respond with a simple greeting and tell me you are working through Claude Code Router.\"}],\"model\":\"gemini-2.5-pro\",\"max_tokens\":100}","signal":{}},"requestUrl":"https://ovoai.apicenter.top/openai/v1/chat/completions","msg":"final request"}
{"level":50,"time":1754918123295,"pid":34592,"hostname":"LIU","reqId":"req-1","err":{"type":"SyntaxError","message":"Unexpected token '<', \"<!doctype \"... is not valid JSON","stack":"SyntaxError: Unexpected token '<', \"<!doctype \"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at parseJSONFromBytes (node:internal/deps/undici/undici:5738:19)\n    at successSteps (node:internal/deps/undici/undici:5719:27)\n    at fullyReadBody (node:internal/deps/undici/undici:4609:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async consumeBody (node:internal/deps/undici/undici:5728:7)\n    at async _s.transformResponseIn (C:\\Users\\<USER>\\.npm-global\\node_modules\\@musistudio\\claude-code-router\\dist\\cli.js:74988:15)\n    at async u0 (C:\\Users\\<USER>\\.npm-global\\node_modules\\@musistudio\\claude-code-router\\dist\\cli.js:74719:46)\n    at async o0 (C:\\Users\\<USER>\\.npm-global\\node_modules\\@musistudio\\claude-code-router\\dist\\cli.js:74678:116)"},"msg":"Unexpected token '<', \"<!doctype \"... is not valid JSON"}
{"level":30,"time":1754918123298,"pid":34592,"hostname":"LIU","reqId":"req-1","res":{"statusCode":500},"responseTime":1619.8237999994308,"msg":"request completed"}
