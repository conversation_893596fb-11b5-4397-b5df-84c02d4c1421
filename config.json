{
  "LOG": true,
  "API_TIMEOUT_MS": 60000,
  "PROXY_URL": "",
  "NON_INTERACTIVE_MODE": false,
  "Providers": [
    {
      "name": "qdgf",
      "api_base_url": "https://ovoai.apicenter.top/v1/chat/completions",
      "api_key": "sk-KdujPK1u6uDT51tbZyLRkFn7rxhuFllaCJ37ioBVT8Fi8Uk8",
      "models": ["gemini-2.5-pro"]
      "transformer":{
        "use": ["qdgf"]
      }
    }
  ],
  "Router": { 
    "default": "qdgf,gemini-2.5-pro",
    "background": "qdgf,gemini-2.5-pro",
    "think": "qdgf,gemini-2.5-pro",
    "longContext": "qdgf,gemini-2.5-pro",
    "longContextThreshold": 60000,
    "webSearch": "qdgf,gemini-2.5-pro"
  }
}
