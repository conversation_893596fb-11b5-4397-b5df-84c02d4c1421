{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: Anthropic (endpoint: /v1/messages)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: gemini (endpoint: /v1beta/models/:modelAndAction)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: vertex-gemini (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: vertex-claude (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: deepseek (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: tooluse (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: openrouter (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: maxtoken (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: groq (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: cleancache (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: enhancetool (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: reasoning (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: sampling (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: maxcompletiontokens (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: cerebras (no endpoint)"}
{"level":30,"time":1754917618638,"pid":26832,"hostname":"LIU","msg":"register transformer: streamoptions (no endpoint)"}
{"level":30,"time":1754917618641,"pid":26832,"hostname":"LIU","msg":"ovoai provider registered"}
{"level":30,"time":1754917618682,"pid":26832,"hostname":"LIU","msg":"Server listening at http://127.0.0.1:3456"}
{"level":30,"time":1754917618682,"pid":26832,"hostname":"LIU","msg":"🚀 LLMs API server listening on http://127.0.0.1:3456"}
{"level":30,"time":1754917650177,"pid":26832,"hostname":"LIU","reqId":"req-1","req":{"method":"POST","url":"/v1/messages","host":"127.0.0.1:3456","remoteAddress":"127.0.0.1","remotePort":64016},"msg":"incoming request"}
{"level":30,"time":1754917650182,"pid":26832,"hostname":"LIU","reqId":"req-1","body":{"messages":[{"content":"Hello! Please respond with a simple greeting and tell me which model you are.","role":"user"}],"model":"ovoai,gemini-2.5-pro","max_tokens":100},"msg":"request body"}
{"level":20,"time":1754917650184,"pid":26832,"hostname":"LIU","request":{"method":"POST","headers":{},"body":"{\"messages\":[{\"content\":\"Hello! Please respond with a simple greeting and tell me which model you are.\",\"role\":\"user\"}],\"model\":\"gemini-2.5-pro\",\"max_tokens\":100}","signal":{}},"requestUrl":"https://ovoai.apicenter.top/chat/completions","msg":"final request"}
{"level":50,"time":1754917651638,"pid":26832,"hostname":"LIU","reqId":"req-1","err":{"type":"TypeError","message":"fetch failed: Request body length does not match content-length header","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at async a0 (C:\\Users\\<USER>\\.npm-global\\node_modules\\@musistudio\\claude-code-router\\dist\\cli.js:74708:11)\n    at async o0 (C:\\Users\\<USER>\\.npm-global\\node_modules\\@musistudio\\claude-code-router\\dist\\cli.js:74678:84)\ncaused by: RequestContentLengthMismatchError: Request body length does not match content-length header\n    at AsyncWriter.end (C:\\Users\\<USER>\\.npm-global\\node_modules\\@musistudio\\claude-code-router\\dist\\cli.js:42023:19)\n    at writeIterable (C:\\Users\\<USER>\\.npm-global\\node_modules\\@musistudio\\claude-code-router\\dist\\cli.js:41912:16)"},"msg":"fetch failed"}
{"level":30,"time":1754917651641,"pid":26832,"hostname":"LIU","reqId":"req-1","res":{"statusCode":500},"responseTime":1463.5529999993742,"msg":"request completed"}
